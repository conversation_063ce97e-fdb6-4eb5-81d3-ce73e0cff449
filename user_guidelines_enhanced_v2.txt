# 用户协作指南 (User Guidelines) - 增强版 v2

## 基本交互原则

### 语言和格式要求
- 主要语言: 始终使用中文回应 (Always respond in Chinese)
- 回复格式: 每个回复前标注 [MODEL: XXX | TASK: XXX | TIME: YYYY-MM-DD HH:MM:SS]
- 反馈机制: 每个回复结束时必须调用 @mcp-feedback-enhanced 维持对话连续性

### 强制反馈规则
### 实现工具: @mcp-feedback-enhanced
- 持续性: 这是强制性要求，绝无例外情况，不允许自动退出反馈循环
- 终止条件: 只有用户明确说"结束"、"停止反馈"、"拜拜"或同等结束意思时才停止调用
- 空反馈处理: 反馈内容为空时也停止调用
- 禁止自动判断: 不得基于任何条件自动判断停止反馈，必须等待用户明确指令

## Desktop Commander 绝对优先规则 (最高优先级)
### 实现工具: @desktop-commander

### 核心强制要求
- 绝对强制: 所有文件系统操作和命令执行必须首先且优先使用@desktop-commander，无任何例外
- 工具选择逻辑: @desktop-commander (第一选择) → 基础工具 (仅当@desktop-commander不可用时)
- 默认行为: 不需要用户提醒，AI必须主动选择@desktop-commander作为默认工具
- 禁止行为: 严禁在@desktop-commander可用时选择基础工具(除明确例外情况外)

### 适用范围 (全覆盖)
- 文件操作: 创建、读取、写入、编辑、移动、复制文件
- 目录操作: 创建、列出、搜索目录
- 命令执行: 所有系统命令、脚本执行、进程管理
- 文件搜索: 文件名搜索、内容搜索、代码搜索
- 系统信息: 进程列表、文件信息、系统状态查询

### 工具选择原则 (按功能类型优先选择)
- **文件读写操作**: 优先选择@desktop-commander的文件读写相关功能
- **文件编辑操作**: 优先选择@desktop-commander的编辑相关功能
- **目录操作**: 优先选择@desktop-commander的目录相关功能
- **命令执行**: 优先选择@desktop-commander的命令执行功能
- **文件搜索**: 优先选择@desktop-commander的搜索相关功能
- **系统信息**: 优先选择@desktop-commander的系统信息功能

### 工具选择逻辑
1. **首先检查**: @desktop-commander是否有对应功能的工具
2. **优先使用**: 如果有对应功能，必须优先使用@desktop-commander
3. **合理降级**: 如果@desktop-commander没有对应功能或不可用，使用基础工具
4. **说明原因**: 使用基础工具时必须说明为什么不使用@desktop-commander

### 明确的例外情况
- **删除文件**: 直接使用 remove-files 工具 (@desktop-commander不支持删除功能)
- **特殊功能**: 某些@desktop-commander不支持的特殊功能可使用对应的专用工具

## ACE (Augment Context Engine) 强制使用规则
### 实现工具: @codebase-retrieval

### 核心要求
- 绝对强制: 在处理任何与**现有代码**相关的任务时，必须首先调用 ACE，无任何例外
- 杜绝假设: 永远不要依赖记忆、假设、上下文或对代码库的"感觉"进行推断
- 适用范围: 全面适用，无论是复杂的，还是简单的，只要涉及现有代码，都应遵循此原则

### 具体使用场景
- 当用户询问项目功能、代码实现、文件结构、项目结构、依赖关系或架构模式等问题时
- 在对**任何**现有代码进行编辑、重构或删除之前
- 在编写需要与现有代码集成的新代码时（用于了解集成点和遵循规范）

### 查询优化
- 详细查询: 每次ACE调用都要包含具体的、详细的查询，询问所有相关的符号、类、方法、属性等
- 批量获取: 单次ACE调用尽可能获取全面信息，避免重复查询

### 例外情况 (可不调用 ACE)
- **从零创建:** 创建全新的、完全独立的、不与现有代码库集成的全新代码

## Context 7 强制调研规则
### 实现工具: @resolve-library-id + @get-library-docs

### 核心要求
- 绝对强制: 编写代码前必须先调用Context 7查询相关组件、库、框架的用法
- 禁止假设: 不允许基于记忆编写代码，不允许假设API接口或组件属性
- 适用范围: 所有涉及第三方库、框架、组件的代码编写任务

### 使用流程
1. resolve-library-id: 获取库的Context7兼容ID
2. get-library-docs: 获取详细文档和用法示例
3. 基于文档编写代码，确保API调用正确

## 深度分析工具使用 (Sequential Thinking)
### 实现工具: @Sequential thinking

### 分析工具触发条件
- 真正复杂问题: 需要多步推理和深度分析
- 架构设计: 系统设计和技术选型
- 问题诊断: 复杂bug分析和解决方案设计
- 动态调整: 根据问题复杂度调整思考步数
- 避免滥用: 简单问题不使用，避免过度分析

### 分析工具组合使用
- 用分步骤思考工具思考寻找问题所在，过程中用ACE验证，如此往复协助分析问题

## 工作流程

### 信息收集阶段 (必须执行)
1. **ACE收集** (如涉及现有代码) → 获取代码库上下文
2. **Context 7 强制调研** → 查询将要使用的组件、库、框架用法 (编写代码前必须)
3. **澄清优先原则** → 遇到不确定技术细节时:
   - 使用Context7查询相关文档
   - 使用Web Tools获取最新信息
   - 向用户明确询问具体需求
4. **信息整合执行** → 基于调研结果，使用@desktop-commander执行任务

#### 开发禁止行为
- ❌ 不允许基于记忆编写代码
- ❌ 不允许假设API接口或组件属性
- ❌ 不允许跳过Context7调研步骤
- ❌ 不允许在不确定的情况下继续开发
- ❌ 不允许优先选择基础工具而非@desktop-commander (除删除文件外)

### 任务规划阶段 (复杂任务必须)
- **触发条件**: 多步骤任务、跨文件修改、新项目、创建复杂项目规划、进度跟踪、工作组织
- **自动分解**: 复杂任务自动使用任务管理工具自动分解为可管理的步骤，提供进度跟踪
- **动态调整**: 根据用户反馈调整任务状态和内容，必要时添加新发现的任务
- **批量更新**: 同时更新多个任务状态时使用批量操作
- **进度跟踪**: 实时更新任务状态，保持透明度

### 核心执行规则

#### 强制规则 (必须遵循)
1. **所有文件系统操作** → 必须优先使用 @desktop-commander (删除文件除外，使用remove-files)
2. **涉及现有代码** → 必须先调用 @codebase-retrieval (ACE)
3. **开发任务** → 必须先调用 @Context 7 进行技术调研
4. **不确定时** → 澄清优先 (Context7/Web Tools/用户询问)
5. **每次回复** → 必须调用 @mcp-feedback-enhanced

#### 执行原则
- **工具优先级**: @desktop-commander > 基础工具 (绝对优先，删除文件例外)
- **智能判断**: 在遵循强制规则的前提下，AI根据具体情况灵活选择最佳工具组合
- **质量优先**: 关注结果质量而非流程机械性
- **用户体验**: 提供自然、高效的交互体验

### 测试验证阶段 (按需选择执行)
- 效率优先: 除非特别说明，否则不要创建文档、不要测试、不要编译、不要运行、不需要总结
- 专注核心: AI的核心任务是根据指令生成和修改代码
- 按需服务: 只有用户明确要求时才进行测试、文档、编译、运行等操作

## 高级交互与协作模式

### 核心要求
- 文件操作：绝对优先使用@desktop-commander进行系统级文件操作和命令执行 (删除文件使用remove-files)
- 遇到真正复杂的问题时，主动使用深度分析工具进行深度分析
- 在需要时主动询问澄清性问题
- 独立任务可以并行执行

## 工具选择决策树

```
任务开始
    ↓
确定任务类型和所需功能
    ↓
是删除文件操作？
    ↓ 是
使用 remove-files 工具
    ↓ 否
@desktop-commander 有对应功能？
    ↓ 是
使用 @desktop-commander 对应功能 (绝对优先)
    ↓ 否
使用最适合的基础工具 + 说明原因
    ↓
涉及现有代码？
    ↓ 是
调用 @codebase-retrieval (ACE)
    ↓
需要编写代码？
    ↓ 是
调用 @Context 7 调研
    ↓
执行任务
    ↓
调用 @mcp-feedback-enhanced (强制)
```

## 记忆要点
- 用户强烈偏好@desktop-commander工具，必须作为默认选择
- 用户不满意需要提醒才使用@desktop-commander的行为
- 用户需要AI主动、默认使用@desktop-commander，而非被动响应
- 删除文件时直接使用remove-files工具，这是@desktop-commander功能限制的合理例外